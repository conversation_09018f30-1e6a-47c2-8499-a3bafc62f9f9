import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";
import { Client } from 'typesense';
import { Product, ProductSearchResult, FreebieProduct } from '../models';
import { CommonService } from './common.service';
@Injectable({
  providedIn: 'root'
})
export class TypeSenseService {
  client: Client | any;

  constructor(
    private commonService: CommonService
  ) {
    this.initiTypesense();
  }

  initiTypesense() {
    this.client = new Client({
      nodes: [{
        host: environment.typesense.host,
        port: Number(environment.typesense.port),
        protocol: environment.typesense.protocol,
      }],
      apiKey: environment.typesense.apiKey,
      connectionTimeoutSeconds: 2,
    });
  }

  async getStoreById(): Promise<any> {
    try {
      const currentLocation = window.location.hostname;
      const params = { q: '*', filter_by: `domain:=${currentLocation}`, per_page: 1 };
      const response = await this.client.collections('stores').documents().search(params);
      return response.hits?.[0]?.document || {};
    } catch (error) {
      console.error('Error fetching store:', error);
      return {};
    }
  }

  async getProductBySku(child_sku: string[], perPage: number = 100): Promise<Product[] | null> {
    const storeId = this.commonService.currentFacility?.facilityId || '936185';
    try {
      const params = {
        q: '*',
        query_by: 'child_sku',
        filter_by: `store_id:=${storeId} && child_sku:=[${child_sku.join(',')}]`,
        include_fields: 'display_alias,name,thumbnail_image,variant_name,selling_price,child_sku,ean_number,tax,cgst,sgst,igst,cess,unit_price',
        page: 1,
        per_page: perPage * 10
      }
      let response: any = await this.client.collections('facility_products').documents().search(params);
      const products = this.productsResponseMapper(response);
      return products;
    } catch (error) {
      console.error('Error fetching product by SKU:', error);
      return null;
    }
  }

  async searchProductsDirectly(
    searchTerm: string,
  ): Promise<ProductSearchResult> {
    try {
      const searchParams = {
        q: searchTerm,
        query_by: 'ean_number,child_sku,name,display_alias',
        prefix: 'true,true,true,true',
        filter_by: `store_id:=${this.commonService.currentFacility?.facilityId || '936185'}`,
        include_fields: 'display_alias,name,thumbnail_image,variant_name,selling_price,child_sku,ean_number,tax,cgst,sgst,igst,cess,unit_price',
        prioritize_exact_match: 'true'
      };
      let response: any = {};
      try {
        response = await this.client.collections('facility_products').documents().search(searchParams);
      } catch (error) {
        response = await this.client.collections('store_products').documents().search(searchParams);
      }
      const products = this.productsResponseMapper(response);
      return {
        products: products,
        totalProducts: response.found
      };
    } catch (error) {
      console.error('Error in searchProductsDirectly:', error);
      return {
        products: [],
        totalProducts: 0
      };
    }
  }

  // Get freebies products for cart amount
  async getFreebiesProducts(cartAmount: number = 0): Promise<FreebieProduct[] | null> {
    try {
      const currentTime = Math.floor(Date.now() / 1000);
      const response: any = await this.client.collections('freebies_products').documents().search({
        q: '*',
        sort_by: 'amount:desc',
        filter_by: `store_id:=936185 && amount:<=${cartAmount} && start_date:<=${currentTime} && end_date:>=${currentTime} && available_qty:>0`,
        page: 1,
        per_page: 100
      });

      if (response.hits?.length > 0) {
        const freebies = response.hits.map((hit: any) => hit.document);
        return await this.getFreebieProductDetails(freebies);
      }
      return [];
    } catch (error) {
      console.error('Error fetching freebies:', error);
      return null;
    }
  }

  async getFreebieProductDetails(freebies: any[]): Promise<any[]> {
    try {
      const productIds = freebies.map(freebie => freebie.product_id);
      const productResponse: any = await this.client.collections('facility_products').documents().search({
        q: '*',
        filter_by: `store_id:=936185 && product_id:=[${productIds.join(',')}]`,
        include_fields: 'product_id,thumbnail_image,selling_price,variant_name,child_sku',
        page: 1,
        per_page: 100
      });

      const products = productResponse.hits?.map((hit: any) => hit.document) || [];

      return freebies.map(freebie => {
        const product = products.find((p: any) => p.product_id === freebie.product_id);
        return {
          ...freebie,
          thumbnail_image: product?.thumbnail_image,
          selling_price: product?.selling_price || 0,
          variant_name: product?.variant_name,
          child_sku: product?.child_sku
        };
      });
    } catch (error) {
      console.error('Error getting freebie product details:', error);
      return freebies;
    }
  }

  productsResponseMapper(response: Record<string, any>): Product[] {
    return response['hits']?.map((hit: Record<string, any>) => ({...hit['document'], taxable: false})) || [];
  }
}