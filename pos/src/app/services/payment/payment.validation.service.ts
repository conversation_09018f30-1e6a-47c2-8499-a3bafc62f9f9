import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';
import { PaymentConfigService, PaymentMethod } from './payment.config.service';

@Injectable({
  providedIn: 'root'
})
export class PaymentValidationService {
  constructor(
    private messageService: MessageService,
    private paymentConfigService: PaymentConfigService
  ) {}

  validateForm(paymentData: any, customerName: string, customerPhone: string, totalAmount: number): boolean {
    // Validate customer info
    if (!customerName?.trim()) {
      this.showError('Please enter customer name');
      return false;
    }
    if ( !customerPhone?.trim()) {
      this.showError('Please enter customer phone');
      return false;
    }

    const method = paymentData.selectedPaymentMethod;
    const methodData = this.paymentConfigService.getPaymentMethod(method, totalAmount);

    if (!methodData) {
      this.showError('Invalid payment method');
      return false;
    }

    // Validate required fields for the selected payment method
    if (!this.validateRequiredFields(paymentData, methodData)) {
      return false;
    }

    // Validate payment amount
    if (!this.validatePaymentAmount(paymentData, method, totalAmount)) {
      return false;
    }

    return true;
  }

  private validateRequiredFields(paymentData: any, methodData: PaymentMethod): boolean {
    for (const field of methodData.fields) {
      if (field.required) {
        const value = paymentData[field.key];
        if (value === undefined || value === null || value === '') {
          this.showError(`${field.label} is required`);
          return false;
        }
      }
    }
    return true;
  }

  private validatePaymentAmount(paymentData: any, method: string, totalAmount: number): boolean {
    const amountKey = `${method}Amount`;
    const amount = paymentData[amountKey];
    
    if (amount <= 0) {
      this.showError('Payment amount must be greater than 0');
      return false;
    }

    if (amount < totalAmount) {
      this.showError('Payment amount must be at least equal to the total amount');
      return false;
    }

    if (amount > totalAmount) {
      this.showError('Payment amount is too high');
      return false;
    }

    return true;
  }

  private showError(detail: string): void {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail,
    });
  }
}
