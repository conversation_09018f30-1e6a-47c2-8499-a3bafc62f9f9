import { Injectable } from '@angular/core';

export interface PaymentMethodField {
  key: string;
  label: string;
  type: 'amount' | 'text' | 'radio' | 'select';
  required?: boolean;
  min?: number;
  max?: number;
  placeholder?: string;
  options?: { label: string; value: any }[];
  showButtons?: boolean;
}

export interface PaymentMethod {
  value: string;
  label: string;
  icon: string;
  color: string;
  fields: PaymentMethodField[];
  addUserButton?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class PaymentConfigService {
  private paymentMethods: PaymentMethod[] = [];

  constructor() {
    this.initializePaymentMethods();
  }

  private initializePaymentMethods(): void {
    this.paymentMethods = [
      {
        value: 'cash',
        label: 'Cash',
        icon: 'pi pi-money-bill',
        color: 'green',
        fields: [
          {
            key: 'cashAmount',
            label: 'Amount Received',
            type: 'amount',
            required: true,
            min: 0,
            max: 1000000, // Will be updated with actual total amount
            showButtons: true,
          },
        ],
      },
      {
        value: 'upi',
        label: 'UPI',
        icon: 'pi pi-wallet',
        color: 'blue',
        fields: [
          {
            key: 'upiAmount',
            label: 'Amount',
            type: 'amount',
            required: true,
            min: 1,
            max: 1000000, // Will be updated with actual total amount 
            showButtons: true,
          },
          {
            key: 'upiId',
            label: 'UPI ID',
            type: 'text',
            placeholder: 'example@upi',
          },
        ],
      },
      {
        value: 'card',
        label: 'Card',
        icon: 'pi pi-credit-card',
        color: 'indigo',
        fields: [
          {
            key: 'cardAmount',
            label: 'Amount',
            type: 'amount',
            required: true,
            min: 1,
            max: 1000000, // Will be updated with actual total amount
            showButtons: true,
          },
          {
            key: 'cardType',
            label: 'Card Type',
            type: 'radio',
            options: [
              { label: 'Credit', value: 'credit' },
              { label: 'Debit', value: 'debit' },
            ],
          },
        ],
      },
      {
        value: 'partial',
        label: 'Partial',
        icon: 'pi pi-wallet',
        color: 'orange',
        fields: [
          {
            key: 'partialAmount',
            label: 'Amount',
            type: 'amount',
            required: true,
            min: 1,
            max: 1000000, // Will be updated with actual total amount
            showButtons: true,
          },
        ],
      },
    ];
  }

  getPaymentMethods(totalAmount: number = 0): PaymentMethod[] {
    // Update max amount for each payment method based on total amount
    return this.paymentMethods.map(method => ({
      ...method,
      fields: method.fields.map(field => {
        if (field.type === 'amount' && field.key.endsWith('Amount')) {
          return {
            ...field,
            max: totalAmount * 2,
          };
        }
        return field;
      }),
    }));
  }

  getPaymentMethod(methodValue: string, totalAmount: number = 0): PaymentMethod | undefined {
    return this.getPaymentMethods(totalAmount).find(m => m.value === methodValue);
  }

  getDefaultPaymentData(): any {
    return {
      selectedPaymentMethod: 'cash',
      cashAmount: 0,
      upiAmount: 0,
      upiId: '',
      cardAmount: 0,
      cardType: 'credit',
      partialAmount: 0,
    };
  }

  initializePaymentData(totalAmount: number): any {
    const data = this.getDefaultPaymentData();
    
    // Set default amounts based on total
    if (totalAmount > 0) {
      data.cashAmount = totalAmount;
      data.upiAmount = totalAmount;
      data.cardAmount = totalAmount;
      data.partialAmount = totalAmount;
    }
    
    return data;
  }
}
