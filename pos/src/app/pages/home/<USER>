import { ChangeDetectorRef, Component, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TypeSenseService } from '../../services/typesense.service';
import { Product, CartItem, ProductSearchResult } from '../../models';
import { FreebieProduct } from '../../models/freebie.model';
import { TableComponent } from "src/app/components/table/table";
import { AutoCompleteModule } from 'primeng/autocomplete';
import { IftaLabelModule } from 'primeng/iftalabel';
import { TabsModule } from 'primeng/tabs';
import { BillingComponent } from 'src/app/components/billing/billing';
import { CartCalculationUtils } from 'src/app/utils/cart-calculation.utils';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    BillingComponent,
    TableComponent,
    AutoCompleteModule,
    IftaLabelModule,
    TabsModule,
    IonicModule
  ],
})
export class HomePage implements AfterViewInit {
  @ViewChild('searchInput') searchInput!: ElementRef;
  currentTab = 0;
  billingTabs = [
    {
      title: 'Billing',
      value: 0,
      items: [] as CartItem[],
      // Tab-specific freebie properties
      showFreebies: false,
      freebiesProducts: [] as FreebieProduct[],
      currentCartAmount: 0,
      addedFreebies: new Set<string>()
    }
  ];
  searchSuggestions: Product[] = [];
  searchText: string = '';
  productsColumns: any[] = [];

  // Global freebie columns (shared across all tabs)
  freebiesColumns: any[] = [];

  constructor(
    public typesenseService: TypeSenseService,
    private cdr: ChangeDetectorRef,
  ) { }

  ionViewDidEnter() {
    this.searchSuggestions = [];
    this.currentTab = 0;
    this.searchText = '';
    this.productsColumns = [
      { field: 'thumbnail_image', header: 'Image', type: 'image', width: '100px' },
      {
        field: 'name',
        header: 'Item Name',
        width: '100px',
        body: (item: any) => item.is_freebie ? `${item.name} (FREE)` : item.name,
        class: (item: any) => item.is_freebie ? 'text-green-600 font-semibold' : ''
      },
      { field: 'variant_name', header: 'Variant Name', width: '150px' },
      { field: 'child_sku', header: 'SKU', width: '100px' },
      {
        field: 'selling_price',
        header: 'Price',
        body: (item: any) => item.is_freebie ? 'FREE' : CartCalculationUtils.formatCurrency(item.selling_price),
        class: (item: any) => item.is_freebie ? 'text-green-600 font-semibold' : 'text-orange-600 font-semibold',
        width: '100px'
      },
      { field: 'discount', header: 'Discount', body: (item: any) => CartCalculationUtils.formatCurrency(CartCalculationUtils.calculateItemDiscount(item)), width: '100px' },
      { field: 'total_amount', header: 'Total Amt', body: (item: any) => CartCalculationUtils.formatCurrency(CartCalculationUtils.calculateItemTotal(item)), width: '100px' },
      {
        field: 'quantity',
        header: 'Quantity',
        type: 'quantity',
        width: '100px',
        disabled: (item: any) => item.is_freebie // Disable quantity editing for freebies
      },
      { header: 'Actions', type: 'action', buttons: [{ icon: 'pi pi-trash', id: 'remove', rounded: true, outlined: true, severity: 'danger' }], width: '50px' },
    ];

    // Initialize freebies columns
    this.freebiesColumns = [
      { field: 'thumbnail_image', header: 'Image', type: 'image', width: '80px' },
      { field: 'name', header: 'Item Name', width: '150px' },
      { field: 'variant_name', header: 'Variant', width: '100px' },
      { field: 'amount', header: 'Min Amount', body: (item: any) => CartCalculationUtils.formatCurrency(item.amount), class: 'text-green-600 font-semibold', width: '100px' },
      { field: 'available_qty', header: 'Available', width: '80px' },
      {
        header: 'Actions',
        type: 'action',
        buttons: [{
          icon: 'pi pi-plus',
          id: 'add_freebie',
          rounded: true,
          outlined: true,
          severity: 'success',
          label: 'Add',
          hide: (item: any) => this.isFreebieInCart(item) || this.billingTabs[this.currentTab].currentCartAmount < item.amount
        }],
        width: '80px'
      },
    ];

    // Initialize freebies
    this.initializeFreebies();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.focusSearch();
    }, 100);
  }

  focusSearch() {
    if (this.searchInput?.nativeElement) {
      const inputElement = this.searchInput.nativeElement.querySelector('input');
      if (inputElement) {
        inputElement.focus();
        inputElement.setSelectionRange(
          inputElement.value.length,
          inputElement.value.length,
        );
      }
    }
  }

  onSearch(event: { query: string }) {
    const query = event.query;
    if (!query || query.trim() === '') {
      this.searchSuggestions = [];
      return;
    }
    this.typesenseService.searchProductsDirectly(query.trim()).then((result: ProductSearchResult) => {
      this.searchSuggestions = result.products || [];
    }).catch((error: any) => {
      console.error('Search error:', error);
      this.searchSuggestions = [];
    });
  }

  onEnterKey(event: any) {
    if (this.searchSuggestions?.length > 0) {
      event.preventDefault();
      event.stopPropagation();
      this.onSearchSelect({ value: this.searchSuggestions[0] });
    }
  }

  onSearchSelect(event: { value: Product } | Product) {
    const selectedProduct = 'value' in event ? event.value : event;
    if (selectedProduct) {
      this.addToCart(selectedProduct);
      this.searchSuggestions = [];
      this.searchText = '';
      setTimeout(() => {
        this.focusSearch();
      }, 100);
    }
  }

  onCartChange(_cartItems: CartItem[]) {
    this.refreshDisplayAndDetectChanges();
    this.checkCartAmountAndUpdateFreebies();
  }

  billingGrandTotal() {
    return this.billingTabs[this.currentTab].items.reduce((total, item) =>
      total + ((item.selling_price || 0)) * (item.quantity || 0), 0
    );
  }

  // Initialize freebies on page load
  async initializeFreebies() {
    await this.updateCartAmountAndFreebies();
  }

  // Check cart amount and update freebies when cart changes
  async checkCartAmountAndUpdateFreebies() {
    const currentTab = this.getCurrentTab();
    const newCartAmount = this.billingGrandTotal();

    if (newCartAmount !== currentTab.currentCartAmount) {
      console.log(`Tab ${this.currentTab}: Cart amount changed from ${currentTab.currentCartAmount} to ${newCartAmount}`);
      await this.updateCartAmountAndFreebies();
    }
  }

  private getCurrentTab() {
    return this.billingTabs[this.currentTab];
  }

  private async updateCartAmountAndFreebies() {
    const currentTab = this.getCurrentTab();
    currentTab.currentCartAmount = this.billingGrandTotal();
    await this.updateFreebies();
    await this.manageAutomaticFreebies();
  }

  // Fetch and update available freebies based on cart amount
  async updateFreebies() {
    const currentTab = this.getCurrentTab();
    try {
      const freebies = await this.typesenseService.getFreebiesProducts(currentTab.currentCartAmount);
      const highestQualifyingFreebie = freebies?.[0];

      this.setFreebiesDisplay(currentTab, highestQualifyingFreebie);
      this.refreshFreebiesDisplay();
    } catch (error) {
      console.error('Error updating freebies:', error);
      this.clearFreebiesDisplay(currentTab);
    }
  }

  private setFreebiesDisplay(currentTab: any, freebie: any) {
    if (freebie) {
      currentTab.freebiesProducts = [freebie];
      currentTab.showFreebies = true;
      console.log(`Tab ${this.currentTab}: Showing freebie: ${freebie.name} (min: ${freebie.amount}) for cart: ${currentTab.currentCartAmount}`);
    } else {
      this.clearFreebiesDisplay(currentTab);
    }
  }

  private clearFreebiesDisplay(currentTab: any) {
    currentTab.freebiesProducts = [];
    currentTab.showFreebies = false;
  }

  // Refresh freebies display to update button states
  private refreshFreebiesDisplay() {
    const currentTab = this.billingTabs[this.currentTab];
    // Create a new array reference to trigger change detection
    currentTab.freebiesProducts = [...currentTab.freebiesProducts];
    this.cdr.detectChanges();
  }

  // Manage automatic freebie addition/removal based on cart amount
  async manageAutomaticFreebies() {
    const currentTab = this.getCurrentTab();
    const qualifyingFreebie = currentTab.freebiesProducts?.[0];

    if (!qualifyingFreebie || currentTab.currentCartAmount < qualifyingFreebie.amount) {
      this.removeAllFreebiesFromCart();
      return;
    }

    const currentFreebieInCart = this.getCurrentFreebieInCart();
    const needsFreebieUpdate = !currentFreebieInCart ||
      (currentFreebieInCart as any).freebie_id !== qualifyingFreebie.id;

    if (needsFreebieUpdate) {
      this.removeAllFreebiesFromCart();
      this.addFreebieToCart(qualifyingFreebie);
      console.log(`Tab ${this.currentTab}: Updated freebie: ${qualifyingFreebie.name} (min: ${qualifyingFreebie.amount})`);
    }
  }

  private getCurrentFreebieInCart() {
    return this.getCurrentTab().items.find((item: any) => item.is_freebie);
  }

  // Check if a freebie is already in the cart
  private isFreebieInCart(freebie: FreebieProduct): boolean {
    return this.getCurrentTab().items.some((item: any) =>
      item.is_freebie && item.freebie_id === freebie.id
    );
  }

  // Add freebie to cart
  private addFreebieToCart(freebie: FreebieProduct) {
    const currentTab = this.getCurrentTab();
    const freebieCartItem = this.createFreebieCartItem(freebie);

    currentTab.items.push(freebieCartItem);
    currentTab.addedFreebies.add(freebie.id);
    this.refreshFreebiesDisplay();
    console.log(`Tab ${this.currentTab}: Added freebie: ${freebie.name} for cart: ${currentTab.currentCartAmount}`);
  }

  private createFreebieCartItem(freebie: FreebieProduct): any {
    return {
      id: `freebie_${freebie.id}`,
      name: freebie.name,
      child_sku: freebie.child_sku || freebie.sku,
      selling_price: 0,
      quantity: 1,
      tax: 0, cgst: 0, sgst: 0, igst: 0, cess: 0,
      taxable: false,
      thumbnail_image: freebie.thumbnail_image,
      variant_name: freebie.variant_name,
      is_freebie: true,
      freebie_id: freebie.id,
      freebie_amount: freebie.amount,
      freebie_name: freebie.name
    };
  }

  // Remove all freebies from cart
  private removeAllFreebiesFromCart() {
    const currentTab = this.getCurrentTab();
    const initialLength = currentTab.items.length;

    currentTab.items = currentTab.items.filter((item: any) => !item.is_freebie);

    if (currentTab.items.length < initialLength) {
      currentTab.addedFreebies.clear();
      this.refreshFreebiesDisplay();
      console.log(`Tab ${this.currentTab}: Removed all freebies from cart`);
    }
  }

  addToCart(product: Product) {
    const manualQuantity = product.quantity && product.quantity > 0 ? product.quantity : 1;
    this.updateCartItemQuantity(product, manualQuantity, true);
    product.quantity = 1;
    this.refreshDisplayAndDetectChanges();
    // Check for freebies after adding product
    this.checkCartAmountAndUpdateFreebies();
  }

  private updateCartItemQuantity(product: Product | string, quantity: number, isAddition: boolean = false) {
    const productSku = typeof product === 'string' ? product : product.child_sku;
    const cartItem = this.billingTabs[this.currentTab].items.find(
      (item: any) => item.child_sku === productSku,
    );

    if (cartItem) {
      if (isAddition) {
        cartItem.quantity += quantity;
      } else {
        cartItem.quantity = quantity;
        if (cartItem.quantity <= 0) {
          this.removeFromCart({ child_sku: productSku });
          return;
        }
      }
    } else if (isAddition && typeof product === 'object') {
      const newCartItem = { ...product, quantity: quantity, tax: product.tax };
      this.billingTabs[this.currentTab].items.push(newCartItem);
    }
  }

  private refreshDisplayAndDetectChanges() {
    this.updateDisplayedProductsFromCart();
    this.cdr.detectChanges();
  }

  removeFromCart(product: { child_sku: string }) {
    const currentTab = this.getCurrentTab();
    if (!currentTab.items) return;

    const index = currentTab.items.findIndex((item: any) => item.child_sku === product.child_sku);
    if (index === -1) return;

    const removedItem = currentTab.items[index];
    this.handleFreebieRemoval(removedItem, currentTab);

    currentTab.items.splice(index, 1);
    this.refreshDisplayAndDetectChanges();
    this.refreshFreebiesDisplay();
  }

  private handleFreebieRemoval(removedItem: any, currentTab: any) {
    if (removedItem.is_freebie && removedItem.freebie_id) {
      currentTab.addedFreebies.delete(removedItem.freebie_id);
    }
  }

  updateDisplayedProductsFromCart() {
    if (this.billingTabs[this.currentTab].items && this.billingTabs[this.currentTab].items.length > 0) {
      this.billingTabs[this.currentTab].items = [...this.billingTabs[this.currentTab].items].map(item => ({
        ...item,
        quantity: item.quantity
      }));
    } else {
      this.removeTab(this.currentTab);
    }
  }
  onChange(ev: any) {
    const { event, rowData, column } = ev;
    if (column.field === 'quantity') {
      // Don't allow quantity changes for freebies
      if ((rowData as any).is_freebie) {
        console.warn('Cannot change quantity of freebie items');
        return;
      }

      rowData.quantity = event.value;
      this.updateCartItemQuantity(rowData, event.value, false);
      this.refreshDisplayAndDetectChanges();
      // Check for freebies after quantity change
      this.checkCartAmountAndUpdateFreebies();
    }
  }
  onActionClick(ev: any) {
    const { button, rowData } = ev;
    if (button.id === 'remove') {
      this.removeFromCart(rowData);
    } else if (button.id === 'add_freebie') {
      this.addFreebieManually(rowData);
    }
  }

  // Manually add freebie from the freebies table
  private addFreebieManually(freebie: FreebieProduct) {
    const currentTab = this.getCurrentTab();

    if (!this.canAddFreebie(currentTab, freebie)) return;

    this.removeAllFreebiesFromCart();
    this.addFreebieToCart(freebie);
    this.refreshDisplayAndDetectChanges();
  }

  private canAddFreebie(currentTab: any, freebie: FreebieProduct): boolean {
    if (currentTab.currentCartAmount < freebie.amount) {
      console.warn(`Tab ${this.currentTab}: Cart amount ${currentTab.currentCartAmount} insufficient for freebie requiring ${freebie.amount}`);
      return false;
    }

    if (this.isFreebieInCart(freebie)) {
      console.warn(`Tab ${this.currentTab}: Freebie ${freebie.name} already in cart`);
      return false;
    }

    return true;
  }

  onTabChange(ev: any) {
    this.currentTab = ev;
    // Initialize freebies for the newly selected tab
    this.initializeFreebies();
  }
  removeTab(index: number) {
    if (this.billingTabs.length === 1) {
      this.billingTabs[0].items = [];
    } else {
      this.billingTabs.splice(index, 1);
    }
    this.currentTab = index === 0 ? 0 : this.billingTabs?.length === index ? index - 1 : index;
  }
  addTab() {
    this.billingTabs.push({
      title: 'Billing',
      value: this.billingTabs.length,
      items: [],
      // Initialize tab-specific freebie properties
      showFreebies: false,
      freebiesProducts: [],
      currentCartAmount: 0,
      addedFreebies: new Set<string>()
    });
    this.currentTab = this.billingTabs.length - 1;
    // Initialize freebies for the new tab (should be empty since cart is empty)
    this.initializeFreebies();
  }
}
