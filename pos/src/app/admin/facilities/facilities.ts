import { CommonModule } from '@angular/common';
import { Component, ViewChild } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { TableComponent } from 'src/app/components/table/table';
import { FacilityStoreService } from 'src/app/firebase/facility-store.service';
import { CommonFormComponent } from "src/app/components/common-form/common-form.component";
import { ButtonModule } from 'primeng/button';
import { CommonService } from 'src/app/services/common.service';
import { SharedModule } from 'src/app/shared.module';
import { ConfirmationService } from 'primeng/api';
@Component({
  selector: 'app-facilities',
  templateUrl: './facilities.html',
  imports: [
    CommonModule,
    IonicModule,
    TableComponent,
    CommonFormComponent,
    ButtonModule,
    SharedModule
  ]
})
export class FacilitiesComponent {
  @ViewChild('facilitiesForm') facilitiesForm: CommonFormComponent | any;
  facilitiesData: any[] = [];
  facilitiesFormFields: any[] = [];
  updateData: any = {};
  isLoading: boolean = false;
  defaultFormFields: any[] = [
    { title: 'Facility ID', type: 'input', name: 'facilityId', model: '', required: true },
    { title: 'Facility Name', type: 'input', name: 'facilityName', model: '', required: true },
    { title: 'Facility Code', type: 'input', name: 'facilityCode', model: '', required: true },
  ];
  facilitiesColumns: any[] = [
    { field: 'id', header: 'ID' },
    { field: 'facilityName', header: 'Facility Name' },
    { field: 'facilityCode', header: 'Facility Code' },
    {
      type: 'action', buttons: [
        { icon: 'pi pi-trash', severity: 'danger', outlined: true, label: 'Delete', hide: false, disableDoubleClick: true }], header: 'Action'
    }
  ];
  constructor(
    private facilityStoreService: FacilityStoreService,
    private commonService: CommonService,
    private confirmationService: ConfirmationService
  ) {
  }
  ionViewDidEnter(): void {
    this.facilitiesFormFields = [];
    this.facilitiesData = [];
    this.updateData = {};
    this.getAllFacilities();
  }
  async getAllFacilities() {
    this.isLoading = true;
    this.facilitiesData = await this.facilityStoreService.getAllFacilities();
    this.facilitiesFormFields = this.defaultFormFields;
    this.isLoading = false;
  }
  async createFacility() {
    this.facilitiesForm.getFormData().then((formData: any) => {
      const data = formData.form.value;
      if (this.updateData.isUpdate) {
        this.facilityStoreService.updateFacility(this.updateData.data.id, data).then(() => {
          this.commonService.toast({ severity: 'success', summary: 'Success', detail: 'Facility updated successfully' });
          this.resetForm();
        });
      } else {
        this.facilityStoreService.createFacility(data).then(() => {
          this.commonService.toast({ severity: 'success', summary: 'Success', detail: 'Facility created successfully' });
          this.resetForm();
        });
      }
    });
  }
  onRowClick(event: any) {
    const facilityData = event.rowData;
    this.facilitiesForm.form.setValue({
      facilityId: facilityData.id,
      facilityName: facilityData.facilityName,
      facilityCode: facilityData.facilityCode
    });
    this.facilitiesFormFields[0].readonly = true;
    this.updateData.data = facilityData;
    this.updateData.isUpdate = true;
  }
  resetForm(updateFacility: boolean = true) {
    this.facilitiesForm.form.reset();
    this.facilitiesFormFields[0].readonly = false;
    this.updateData = {};
    updateFacility && this.getAllFacilities();
  }
  async onActionClick(event: any) {
    this.confirmationService.confirm({
      message: 'Do you want to delete this facility?',
      header: 'Danger Zone',
      icon: 'pi pi-info-circle',
      rejectLabel: 'Cancel',
      rejectButtonProps: {label: 'Cancel', severity: 'secondary', outlined: true},
      acceptButtonProps: {label: 'Delete', severity: 'danger'},
      accept: () => {this.deleteFacility(event.rowData);},
      reject: () => {this.commonService.setDisableBtn();},
    });
  }
  async deleteFacility(facilityData: any) {
    this.facilityStoreService.deleteFacility(facilityData.id).then(() => {
      this.commonService.toast({ severity: 'success', summary: 'Success', detail: 'Facility deleted successfully' });
      this.getAllFacilities();
    });
  }
}