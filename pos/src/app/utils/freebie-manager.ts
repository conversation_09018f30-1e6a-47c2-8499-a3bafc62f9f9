import { FreebieService } from '../services/freebie.service';
import { FreebieProduct } from '../models/freebie.model';
import { CartItem } from '../models/product.model';

export interface BillingTab {
  items: CartItem[];
  freebiesProducts: FreebieProduct[];
  showFreebies: boolean;
  currentCartAmount: number;
  addedFreebies: Set<string>;
}

export class FreebieManager {
  
  constructor(private freebieService: FreebieService) {}

  /**
   * Initialize freebies for a tab
   */
  async initializeFreebies(tab: BillingTab, cartAmount: number): Promise<void> {
    tab.currentCartAmount = cartAmount;
    await this.updateFreebies(tab);
    await this.manageAutomaticFreebies(tab);
  }

  /**
   * Update freebies display for a tab
   */
  async updateFreebies(tab: BillingTab): Promise<void> {
    const freebies = await this.freebieService.getAvailableFreebies(tab.currentCartAmount);
    tab.freebiesProducts = freebies;
    tab.showFreebies = freebies.length > 0;
  }

  /**
   * Manage automatic freebie addition/removal
   */
  async manageAutomaticFreebies(tab: BillingTab): Promise<void> {
    const qualifyingFreebie = await this.freebieService.getQualifyingFreebie(tab.currentCartAmount);
    
    if (!qualifyingFreebie || tab.currentCartAmount < qualifyingFreebie.amount) {
      this.removeAllFreebies(tab);
      return;
    }

    if (this.freebieService.needsFreebieUpdate(tab.items, qualifyingFreebie)) {
      this.removeAllFreebies(tab);
      this.addFreebie(tab, qualifyingFreebie);
    }
  }

  /**
   * Add a freebie to the cart
   */
  addFreebie(tab: BillingTab, freebie: FreebieProduct): void {
    const freebieItem = this.freebieService.createFreebieCartItem(freebie);
    tab.items.push(freebieItem);
    tab.addedFreebies.add(freebie.id);
  }

  /**
   * Remove all freebies from cart
   */
  removeAllFreebies(tab: BillingTab): void {
    const initialLength = tab.items.length;
    tab.items = this.freebieService.removeAllFreebies(tab.items);
    
    if (tab.items.length < initialLength) {
      tab.addedFreebies.clear();
    }
  }

  /**
   * Add freebie manually (from freebies table)
   */
  addFreebieManually(tab: BillingTab, freebie: FreebieProduct): boolean {
    if (!this.freebieService.canAddFreebie(tab.currentCartAmount, freebie, tab.items)) {
      return false;
    }

    this.removeAllFreebies(tab);
    this.addFreebie(tab, freebie);
    return true;
  }

  /**
   * Handle item removal (check if it's a freebie)
   */
  handleItemRemoval(tab: BillingTab, removedItem: any): void {
    if (removedItem.is_freebie && removedItem.freebie_id) {
      tab.addedFreebies.delete(removedItem.freebie_id);
    }
  }

  /**
   * Check and update freebies when cart amount changes
   */
  async checkCartAmountAndUpdate(tab: BillingTab, newCartAmount: number): Promise<boolean> {
    if (newCartAmount !== tab.currentCartAmount) {
      tab.currentCartAmount = newCartAmount;
      await this.updateFreebies(tab);
      await this.manageAutomaticFreebies(tab);
      return true;
    }
    return false;
  }
}
