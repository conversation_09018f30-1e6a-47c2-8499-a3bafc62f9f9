import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  OnInit,
  ChangeDetectorRef,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { FloatLabelModule } from 'primeng/floatlabel';

import { PaymentConfigService, PaymentMethod } from '../../services/payment/payment.config.service';
import { PaymentValidationService } from '../../services/payment/payment.validation.service';

@Component({
  selector: 'app-payment-modal',
  standalone: true,
  templateUrl: './payment-modal.html',
  imports: [
    CommonModule,
    FormsModule,
    InputNumberModule,
    DialogModule,
    RadioButtonModule,
    ButtonModule,
    InputTextModule,
    FloatLabelModule,
  ],
})
export class PaymentModalComponent implements OnInit, OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Complete Payment';
  @Input() confirmButtonLabel = 'Confirm Payment';
  @Input() cancelButtonLabel = 'Cancel';
  @Input() confirmText = 'Please review the payment details before confirming.';
  
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<{
    paymentMethod: string;
    paymentData: any;
    customerName: string;
    customerPhone: string;
  }>();
  @Output() addUser = new EventEmitter<void>();

  paymentData: any = {};
  remainingAmount = 0;
  change = 0;
  customerName = '';
  customerPhone = '';
  paymentMethods: PaymentMethod[] = [];

  // Expose Math for template use
  Math = Math;

  constructor(
    private paymentConfigService: PaymentConfigService,
    private paymentValidationService: PaymentValidationService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initializePaymentMethods();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['totalAmount'] && this.totalAmount) {
      this.updateRemainingAmount();
    }
    if (changes['visible'] && this.visible) {
      this.resetPaymentData();
    }
  }

  private initializePaymentMethods(): void {
    this.paymentMethods = this.paymentConfigService.getPaymentMethods(this.totalAmount);
  }

  resetPaymentData(): void {
    this.paymentData = this.paymentConfigService.initializePaymentData(this.totalAmount);
    this.remainingAmount = this.totalAmount;
    this.change = 0;
    this.paymentData.change = 0;
  }

  updateRemainingAmount(): void {
    const selectedMethod = this.paymentData.selectedPaymentMethod;
    const amountKey = `${selectedMethod}Amount`;
    const paidAmount = parseFloat(this.paymentData[amountKey]) || 0;
    
    if (selectedMethod === 'cash' && paidAmount > this.totalAmount) {
      this.remainingAmount = 0;
      this.change = paidAmount - this.totalAmount;
      this.paymentData.change = this.change;
    } else {
      this.remainingAmount = Math.max(0, this.totalAmount - paidAmount);
      this.change = 0;
      this.paymentData.change = 0;
    }
    
    // Force change detection
    this.cdr.detectChanges();
  }

  handlePaymentMethodChange(method: string): void {
    this.paymentData.selectedPaymentMethod = method;
    this.updateRemainingAmount();
  }

  onAmountInput(inputElement: any, fieldKey: string): void {
    // Get the raw value from input and convert to number
    const value = parseFloat(inputElement.value) || 0;
    
    // Update the payment data
    this.paymentData = {
      ...this.paymentData,
      [fieldKey]: value
    };
    
    // Update calculations
    this.updateRemainingAmount();
  }

  getFieldValue(key: string): any {
    return this.paymentData[key];
  }

  setFieldValue(key: string, value: any): void {
    // Ensure we have a valid number, default to 0 if invalid
    const numValue = Number(value) || 0;
    this.paymentData[key] = numValue;
    
    // Force update the view model for the input field
    this.paymentData = { ...this.paymentData };
    
    // Update remaining amount if this is an amount field
    if (key.endsWith('Amount')) {
      this.updateRemainingAmount();
    }
  }

  onDialogHide(): void {
    this.visibleChange.emit(false);
    this.visible = false;
  }

  onCancel(): void {
    this.cancel.emit();
    this.onDialogHide();
  }

  onSubmit(): void {
    if (!this.paymentValidationService.validateForm(
      this.paymentData,
      this.customerName,
      this.customerPhone,
      this.totalAmount
    )) {
      return;
    }

    const paymentData = {
      ...this.paymentData,
      totalAmount: this.totalAmount,
      paidAmount: this.totalAmount - this.remainingAmount,
      remainingAmount: this.remainingAmount,
      paymentMethod: this.paymentData.selectedPaymentMethod,
    };

    this.confirm.emit({
      paymentMethod: this.paymentData.selectedPaymentMethod,
      paymentData,
      customerName: this.customerName,
      customerPhone: this.customerPhone,
    });
  }

  getPaymentDetailClasses(color: string): string {
    return `border-${color}-200 bg-${color}-50`;
  }

  getPaymentDetailIconClasses(color: string): string {
    return `text-${color}-600 text-xl`;
  }

  getPaymentDetailLabelClasses(color: string): string {
    return `text-${color}-800`;
  }

  onAddCustomer(): void {
    this.addUser.emit();
  }
}