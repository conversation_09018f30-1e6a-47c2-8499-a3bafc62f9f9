<p-dialog [header]="title" [modal]="true" [(visible)]="visible" [style]="{width: '50vw', 'max-width':'800px'}"
  [breakpoints]="{'960px': '75vw', '640px': '95vw'}" [closable]="false" [draggable]="false" [resizable]="false">

  <div class="flex flex-col gap-4 p-4 text-md">
    <!-- Customer Information -->
    <div class="customer-info mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
      <div class="flex justify-between items-center mb-3">
        <h6 class="text-lg font-semibold text-gray-800">Customer Information</h6>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p-floatlabel variant="on" styleClass="w-full">
            <input pInputText id="customerName" [(ngModel)]="customerName" [disabled]="isProcessing" autocomplete="off"
              class="w-full" />
            <label for="customerName">Customer Name</label>
          </p-floatlabel>
        </div>
        <div>
          <p-floatlabel variant="on" styleClass="w-full">
            <input pInputText id="customerPhone" [(ngModel)]="customerPhone" [disabled]="isProcessing"
              autocomplete="off" class="w-full" />
            <label for="customerPhone">Phone Number</label>
          </p-floatlabel>
        </div>
      </div>
    </div>

    <!-- Payment Methods -->
    <div class="payment-methods">
      <h6 class="text-lg font-semibold mb-3 text-gray-800">Select Payment Method</h6>

      <!-- Payment Method Selection -->
      <div class="grid grid-cols-2 lg:grid-cols-4 gap-3 mb-6">
        <div *ngFor="let method of paymentMethods" (click)="handlePaymentMethodChange(method.value)"
          class="cursor-pointer transition-all duration-200 hover:shadow-md rounded-lg p-3 border-2" [ngClass]="{
               'border-blue-500 bg-blue-50': paymentData.selectedPaymentMethod === method.value,
               'border-gray-200 hover:border-blue-300': paymentData.selectedPaymentMethod !== method.value,
               'opacity-60': isProcessing
             }">
          <div class="flex flex-col items-center justify-center min-h-[70px]">
            <i [class]="method.icon + ' text-2xl mb-2'" [ngClass]="{
                 'text-blue-600': paymentData.selectedPaymentMethod === method.value,
                 'text-gray-600': paymentData.selectedPaymentMethod !== method.value
               }"></i>
            <label class="text-sm font-medium cursor-pointer text-center" [ngClass]="{
                     'text-blue-700': paymentData.selectedPaymentMethod === method.value,
                     'text-gray-700': paymentData.selectedPaymentMethod !== method.value
                   }">
              {{method.label}}
            </label>
          </div>
        </div>
      </div>

      <!-- Payment Details -->
      <div class="payment-details mb-6">
        <ng-container *ngFor="let method of paymentMethods">
          <div *ngIf="paymentData.selectedPaymentMethod === method.value"
            class="border rounded-lg p-4 transition-all duration-300" [ngClass]="getPaymentDetailClasses(method.color)">

            <!-- Payment Header -->
            <div class="flex items-center gap-3 mb-4">
              <i [class]="method.icon + ' ' + getPaymentDetailIconClasses(method.color)"></i>
              <h4 class="text-lg font-semibold" [ngClass]="getPaymentDetailLabelClasses(method.color)">
                {{method.label}} Payment
              </h4>
            </div>

            <!-- Dynamic Payment Form Fields -->
            <div class="space-y-4">
              <div *ngFor="let field of method.fields">
                <!-- Amount Input Field -->
                <div *ngIf="field.type === 'amount'" class="mb-3">
                  <label class="text-sm font-medium text-gray-700 mb-1 block">{{field.label}}</label>
                  <input pInputText #amountInput type="number" [value]="paymentData[field.key] || ''"
                    (input)="onAmountInput(amountInput, field.key)" [min]="field.min" [max]="field.max"
                    [disabled]="isProcessing" class="w-full p-2 border rounded" step="0.01" autocomplete="off">
                </div>

                <!-- Text Input Field -->
                <div *ngIf="field.type === 'text'" class="mb-3">
                  <label class="text-sm font-medium text-gray-700 mb-1 block">{{field.label}}</label>
                  <input type="text" pInputText [placeholder]="field.placeholder || ''"
                    [(ngModel)]="paymentData[field.key]" [disabled]="isProcessing" class="w-full"
                    [ngClass]="{'p-invalid': field.required && !paymentData[field.key]}">
                </div>

                <!-- Radio Button Field -->
                <div *ngIf="field.type === 'radio'" class="mb-3">
                  <label class="text-sm font-medium text-gray-700 mb-2 block">{{field.label}}</label>
                  <div class="flex items-center gap-6">
                    <div *ngFor="let option of field.options" class="flex items-center gap-2">
                      <p-radioButton [name]="field.key" [value]="option.value" [(ngModel)]="paymentData[field.key]"
                        [disabled]="isProcessing" [inputId]="field.key + option.value">
                      </p-radioButton>
                      <label [for]="field.key + option.value" class="cursor-pointer">{{option.label}}</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Order Summary -->
    <div class="order-summary bg-gray-50 p-4 rounded-lg border border-gray-200">
      <div class="grid grid-cols-1 gap-2 text-2xl">
        <div class="flex justify-between">
          <span class="text-gray-800 font-bold">Total Amount :</span>
          <span class="font-bold ">{{ totalAmount | currency:'INR' }}</span>
        </div>

        <!-- Change Amount (only show when there's change) -->
        <div class="flex justify-between" *ngIf="paymentData.change > 0">
          <span class="text-blue-600">Return Amount :</span>
          <span class="font-bold text-orange-600">{{ paymentData.change | currency:'INR' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Buttons -->
  <ng-template pTemplate="footer">
    <div class="flex gap-2 justify-end w-full">
      <button pButton pRipple severity="danger" type="button" label="{{cancelButtonLabel}}" class="p-button-text"
        [disabled]="isProcessing" (click)="onCancel()">
      </button>

      <div class="flex items-center gap-2">
        <span *ngIf="isProcessing" class="text-sm text-gray-600 flex items-center">
          <i class="pi pi-spin pi-spinner mr-2"></i> Processing...
        </span>
        <button pButton pRipple type="button" [label]="confirmButtonLabel"
          [disabled]="isProcessing || remainingAmount < 0" [loading]="isProcessing" (click)="onSubmit()">
        </button>
      </div>
    </div>
  </ng-template>
</p-dialog>