import { CommonModule } from '@angular/common';
import {Component, Input} from '@angular/core';
import {SkeletonModule} from 'primeng/skeleton';
import {TableModule} from 'primeng/table';
@Component({
  selector: 'app-skeleton',
  templateUrl: './skeleton.component.html',
  imports: [
    SkeletonModule,
    TableModule,
    CommonModule
  ]
})
export class SkeletonComponent {
  @Input() tab = true;
  @Input() table = false;
  @Input() form = false;
  @Input() styleClass = 'p-datatable-modal';
}
