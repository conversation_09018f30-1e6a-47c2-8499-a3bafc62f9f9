import { Component, Input } from "@angular/core";
@Component({
    selector: 'app-no-data',
    standalone: true,
    template: `
    <div class="w-full">
        <div class="w-full h-full flex items-center justify-center">
            <img [width]="width" [src]="image" alt="">
        </div>
        <div>
            <h6 class="text-center" style="margin: 0 !important;">{{title}}</h6>
            <p class="text-gray-400 m-0 text-center" style="margin: 0 !important;">{{description}}</p>
        </div>
    </div>`,
})
export class NoDataComponent {
    @Input() image: string = '';
    @Input() title: string = 'No Data';
    @Input() description: string = 'No data available';
    @Input() width: number = 300;
}